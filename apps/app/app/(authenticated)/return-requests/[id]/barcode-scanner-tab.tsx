'use client';

import { log } from '@repo/observability/log';
import { updateReturnRequest } from '../actions';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
    }>;
  };
}

export function BarcodeScannerTab({ returnRequest }: BarcodeScannerTabProps) {
  const handleItemScanned = (
    itemId: string,
    scannedSku: string,
    quantity: number
  ) => {
    // Here you could also save the scanned status to the database
    log.info(
      `Item ${itemId} scanned with SKU: ${scannedSku}, quantity: ${quantity}`
    );
  };

  const handleSubmit = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    log.info('All items verified:', scannedItems);

    // set processed as true if all items are verified
    await updateReturnRequest(returnRequest.id, {
      processed: true,
    });
  };

  return (
    <BarcodeScanner
      returnItems={returnRequest.returnItems}
      onItemScanned={handleItemScanned}
      onSubmit={handleSubmit}
    />
  );
}
